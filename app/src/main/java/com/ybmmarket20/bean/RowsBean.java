package com.ybmmarket20.bean;

import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.StyleSpan;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.ybm.app.bean.AbstractMutiItemEntity;
import com.ybmmarket20.R;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.xyyreport.page.payment.IPaymentSuiXinPinGoods;
import com.ybmmarket20.xyyreport.paramsInfo.GroupGoodsPlaceInfo;
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo;
import com.ybmmarket20.xyyreport.paramsInfo.OPRowsBeanInfo;
import com.ybmmarket20.xyyreport.spm.TrackData;
import com.ybmmarketkotlin.adapter.goodslist.GoodListBindItemStatus;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Objects;

/**
 * 通用产品数据bean
 */
@Keep
public class  RowsBean extends AbstractMutiItemEntity implements Parcelable, HomeProduct, IRowsBeanInfo, IPaymentSuiXinPinGoods {

    public RowsBean(String showName) {
        this.showName = showName;
    }

    public RowsBean() {

    }

    public int spanSize;
    public static final int CONTENT_11_SPAN_SIZE = 3;
    public static final int CONTENT_31_TEXT_SPAN_SIZE = 1;
    //拼团-进行中
    public static final int SPELL_GROUP_STATUS_PRE_HOT = 0;
    //拼团-预热
    public static final int SPELL_GROUP_STATUS_ACTIVE = 1;
    //非拼团
    public static final int NO_SPELL_GROUP_STATUS = 2;

    public static final int header0 = 1;//头部 0 高度
    public static final int header1 = 2;//头部 50 高度
    public static final int header2 = 3;//头部样式2 70 高度
    public static final int header3 = 4;//头部样式3 90 高度
    public static final int header4 = 5;//头部样式4 110 高度
    public static final int content_11 = 11;//单列
    public static final int content_12 = 12;//单列，图片，140
    public static final int content_13 = 13;//单列，图片.360
    public static final int content_21 = 21;//双列
    public static final int content_22 = 22;//双列,图片，
    //public static final int content_23 = 23;//双列，图片
    public static final int content_31 = 31;//三列,产品，

    public static final int content_32 = 32;


    /*-------------------高毛type-----------------------*/
    public static final int header_41 = 41;
    public static final int header_42 = 42;
    public static final int content_43 = 43;
    public static final int content_44 = 44;


    @SerializedName(value = "id", alternate = "skuId")
    private long id;
    private String showName;
    private String showName;
    private String manufacturer;
    private String spec;        //商品规格
    private long seckillStartTime;//秒杀开始时间
    private int status; //2是已售罄  //4是已下架
    private String retailPrice;     // 划线价
    private String pricePrefix;   // 价格前缀； 如直降价，特价等
    public double fob;
    public String showPriceAfterDiscount;  // 折后价
    private int availableQty;
    public String productUnit;             // 商品单位
    private String imageUrl;
    public int favoriteStatus;//是否收藏--1=收藏，2=没有收藏
    private int isControl;        //是否控销
    private boolean isPurchase;            //是否可以购买 true:可购买 ；false:不可买
    private String markerUrl;//标签图片
    public int mediumPackageNum;//中包装数量 默认为1
    public int isSplit;//是否可拆零 0:不可拆零；1:可拆零 默认1
    private String isSplitTitle;//中包装是否可拆零提示文案
    private String mediumPackageTitle;//中包装显示在加减上面的文案  中包装：500
    private String grossMargin;//毛利率
    private String uniformPrice;//控销价格
    private String suggestPrice;//建议价格
    public String suggestPriceStr;//建议价格 ka用展示
    private List<PriceRangeListBean> skuPriceRangeList;//商品详情价格区间
    //    private boolean gift; //true 显示
    private String blackBGTextDes; //显示价格
    private int priceType;//取fob和价格区间标识  价格类型（1：fob;2:价格区间）
    @Deprecated
    private List<LabelIconBean> tagList;//标签集合
    public TagsWrapperBean tags;       // 重构的标签集合
    private int agent;//是否独家：0否，1是
    private int productNumber; //套餐中单个商品数量
    private int isExpire;//标示是否已过期 1.已过期
    private String favoriterDiffPriceTag;//关注差价文案
    private int businessType;//收藏类型 1: 有货提醒收藏；2：降价收藏；
    private int signStatus;//协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格
    private boolean isOEM;//true是OEM协议商品，为空或者false为非OEM协议商品(此处设置为String类型是因为web页面boolean类型不支持判断是否存在,而为了兼容之前没有OEM需求的情况又需要判断), true/false
    private int agreementEffective;//协议状态(0-未生效,1-生效中)
    private String nearEffect;//近
    private String farEffect;//远
    private double reducePrice; //活动价
    private boolean gift;
    private String companyName;//自营或者非自营name
    private int isThirdCompany;//是否是自营（0：是；1：否）
    private String orgId;//
    private String thirtyDaysAmount;//销量(30天的销量)
    private int IsUsableMedicalStr;//是否医保：0否，1是
    private boolean arrivalReminder;//是否显示到货提醒
    private ActivityTagBean activityTag;//活动标签
    private int highGross;//1:不是髙毛，2:是髙毛
    public String appUrl;

    // 标记在哪里点击了商品详情,这里是用来区分首页推荐，常购，商品模块
    @Deprecated
    public String zhugeEventName;
    public String oftenBuyTypeName;
    public int oftenBuyType;
    public String buyedCountStr;//购买次数
    public List<LabelIconBean> dataTagList;//数据标签列表 标签类型 7, "60天最低价",8, "区域毛利榜" 10, "比上次购买时降xx元",11, "比加入时降xx元",12, "品类点击榜"

    // 一仓卖全国需求，商品上增加店铺跳转链接
    public String shopUrl;
    public String shopName;
    // 拼团
    public ActPtBean actPt;
    // 拼团(这里全都使用小写，跟actPt完全一致)
    @SerializedName("actpt")
    public ActPtBean shopListActPt;
    // 秒杀
    public SeckillBean actSk;

    public PGBYItemBean actPgby;

    // 找相似顶部优惠券
    public TagBean couponTag;


    // 是否签署控销协议 (0没有签署，1签署)
    public int showAgree;
    // 是否控销协议商品（0不是控销协议商品，1是控销协议商品）
    public int isControlAgreement;

    public String nsid;
    public String sdata;
    public String barcode;

    //有效期（近效期/远效期）
    public String effectStr;

    public String controlTitle; //控销价格展示文案 该字段为null或空字符串标识非控销
    public String controlNotes; //控销注释
    public String controlPurchaseButton; //控销购买按钮
    public int controlType; // 5 表示签署协议后可见的状态  这种情况有的地方要显示价格 有的不要显示 看需求

    public String jumpUrl; //商品跳转路由

    public BaseFlowData rowsFlow;
    public int hasSimilarGoods; //更多同款商品:0-否，1-是
    public String masterStandardProductId; //主标准库ID
    public String shopCode;
    public String originalShowName;
    public String sourceType; //枚举数字值{1-策略推荐；99-兜底推荐}
    public ActSuiXinPin actSuiXinPin; //随心拼价格
    @SerializedName("pid")
    public String pId; //主品Id

    public int positionType; //坑位类型
    public String positionTypeName; //坑位类型名称

    public boolean isVirtualSupplier; //是否是虚拟供应商

    public String acptUrl;//活动跳转url

    public String searchSortStrategyCode; //搜索策略编码

    public String operationExhibitionId; //运营位商品组id

    public String operationId; //运营位Id

    public boolean isOPSingleGoods = false; //是否是运营位单品

    public String oPSingleGoodsActName; //运营位单品活动名称
    public String opSingleGoodsActJumpUrl; //运营位单品活动url
    public String directModule; //1-搜索列表信息流 2-搜索运营位模块

    @SerializedName("levelPriceDTO")
    public RangePriceBean rangePriceBean;//平销品阶梯价

    //极光埋点时，手动设置的跟着商品走  不是后台给的
    public JgTrackBean jgTrackBean;

    //搜索页的搜索时的关键词  手动设置的  不是后台给的
    public String searchKeyword;

    // 拼团商品可加车
    public boolean canAddToCart; // 加入购物车
    public boolean freeShippingFlag;
    public String freeShippingText;
    public int productType;
    //商品营销类型
    public String productActivityType;
    //一级分类
    public String categoryFirstId;

    public LimitFullDiscountActInfo limitFullDiscountActInfo;

    public TrackData trackData;

    public String qtSkuData;

    public String qtListData;
    //是否是运营位商品
    public boolean isOpGoods = false;
    //运营位位置
    private int rank;
    private int subRank;
    //运营位商品位置
    private int operationRank;
    //人群id
    private String operationCustomerGroupId;

    public int reloadTag = 0;
    // 12.0.10 新增
    // 进入商详是否显示推荐购买 1:显示  其他：不显示
    public int enterProductDetailIsShowRecPurchase;
    // 进入商详显示哪种推荐购买方式
    public int enterProductDetailShowRecPurchaseType;   // 1:组合购 2：加价购

    public String activityPageId;

    //是否是组合购
    public boolean isSingleCombinationPurchase;
    //是否是加价购
    public boolean isMultipleCombinationPurchase;
    //是否是普通商品
    public boolean isNormalGoods;
    public String scmId;
    public boolean isMainFrequentlyGoods;
    public String listExpId;

    public PaymentSuiXinPinQtData qtData = new PaymentSuiXinPinQtData();

    public boolean isCart = false;



    @Override
    public long getSpmProductId() {
        return id;
    }

    @Nullable
    @Override
    public String getSpmProductName() {
        return showName;
    }

    @Nullable
    @Override
    public String getSpmQtSkuData() {
        return qtSkuData;
    }

    @Nullable
    @Override
    public String getShopCode() {
        return shopCode;
    }

    @Override
    public boolean onOpSingleGoods() {
        return isOPSingleGoods;
    }

    @Nullable
    @Override
    public String getQtListData() {
        return qtListData;
    }

    @Override
    public void setQtListData(@Nullable String qtListData) {
        this.qtListData = qtListData;
    }

    @Override
    public boolean onOpGoods() {
        return isOpGoods;
    }

    @Nullable
    @Override
    public OPRowsBeanInfo getOPRowsBeanInfo() {
        return new OPRowsBeanInfo(rank, operationRank, operationCustomerGroupId, operationExhibitionId);
    }

    @Nullable
    @Override
    public GroupGoodsPlaceInfo getGroupGoodsPlaceInfo() {
        return new GroupGoodsPlaceInfo(rank, subRank, operationCustomerGroupId, operationExhibitionId);
    }

    @Override
    public boolean isSingleGroupPurchase() {
        return isSingleCombinationPurchase;
    }

    @Override
    public boolean isMultipleGroupPurchase() {
        return isMultipleCombinationPurchase;
    }

    @Override
    public boolean isNormalGoods() {
        return isNormalGoods;
    }

    @Override
    public boolean isGroupPurchase() {
        return isSingleCombinationPurchase || isMultipleCombinationPurchase;
    }

    @NonNull
    @Override
    public int getCombinationSelectedStatus() {
        return 0;
    }

    @Nullable
    @Override
    public String getScmId() {
        return qtData.getScmId();
    }

    @Nullable
    @Override
    public String getExpId() {
        return qtData.getExpId();
    }

    @Nullable
    @Override
    public String getRank() {
        return qtData.getRank();
    }

    @Nullable
    @Override
    public String getQtSkuData() {
        return qtData.getQtSkuData();
    }

    @Nullable
    @Override
    public String getProductSkuSkuId() {
        return id + "";
    }

    @Override
    public long getLongSkuId() {
        return id;
    }

    @Nullable
    @Override
    public String getSkuName() {
        return showName;
    }

    @Override
    public boolean isMainFrequently() {
        return isMainFrequentlyGoods;
    }

    @Nullable
    @Override
    public String getListExpId() {
        return listExpId;
    }

    @Nullable
    @Override
    public String getSuiXinPinQtListData() {
        return qtData.getQtListData();
    }

    @Override
    public boolean isCart() {
        return isCart;
    }


    public class ActSuiXinPin {
        public String suiXinPinPrice;
    }


    /**
     * 是否有控销文案
     * @return
     */
    public boolean isControlTitle() {
        return !TextUtils.isEmpty(controlTitle);
    }

    public boolean isNoAgreeAndShowPrice() {  //价格签署协议可见状态时， 显示价格
        return controlType == 5;
    }


    //region 商品标签列表重构

    /**
     * 0 : 正常展示价格
     * 1 ： 展示 "暂无购买权限"
     * 2 :  展示  "价格签署协议可见"
     * 3 :  展示  "价格认证资质可见"
     *
     * @return
     */
    public int showPriceType() {
        int showType = 0;
//        if (isControl == 1 && !isPurchase) {
//            showType = 1;
//        }
//        if ((isOEM == false && isControlAgreement == 1 && showAgree == 0) ||
//                (isOEM == true && signStatus == 0 && isControlAgreement == 1 && showAgree == 0) ||
//                (isOEM == true && signStatus == 0 && isControlAgreement == 0)) {
//            showType = 2;
//        }
//        if (!AuditStatusSyncUtil.getInstance().isAuditFirstPassed()) {
//            showType = 3;
//        }
        if (isControlTitle()) {
            showType = -1;
        }
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 文字 “签署协议后可见” 替换为商品价格；
        if (controlType == 5) {
            showType = 2;
        }
        return showType;
    }

    public boolean isShowPriceType() {
        return (showPriceType() == 0 || showPriceType() == 2);
    }

    /**
     * 是否是控销品
     * @return
     */
    public boolean isControlGoods() {
        return isControlTitle() && controlType == 5;
    }

    /**
     * 是否是拼团中的商品
     * 1. 拼团中的商品
     * 2. 状态为1
     * 3. 剩余时间大于0
     * 4. 可以正常展示价格的
     *
     * @return
     */
    public GoodListBindItemStatus isGroupBookingRow() {
        GoodListBindItemStatus status;
        if (actPt != null && actPt.assembleStatus == 1 && this.status == 2 && isShowPriceType()) {
            //拼团-售罄
            status = new GoodListBindItemStatus.SpellGroupStatusSellOut();
        } else if (actPt != null && actPt.assembleStatus == 1 && (actPt.surplusTime * 1000 > (System.currentTimeMillis() - actPt.responseLocalTime)) && isShowPriceType()) {
            //拼团
            status = new GoodListBindItemStatus.SpellGroupStatusActive();
        } else if (actPt != null && actPt.assembleStatus == 0 && isShowPriceType()) {
            //拼团-预热
            status = new GoodListBindItemStatus.SpellGroupStatusPreHot();
        } else if (actSk != null && isShowPriceType()) {
            //秒杀
            status = new GoodListBindItemStatus.SeckillStatus();
        } else if (actPgby != null && isShowPriceType()) {
            //批购包邮
            status = new GoodListBindItemStatus.PGBYStatus();
        } else {
            //平销品
            status = new GoodListBindItemStatus.NomalStatus();
        }
        return status;
    }

    public String getJgProductType() {
        //不用这个方法了  逻辑判断(actPt.surplusTime * 1000 > (System.currentTimeMillis() - actPt
        // .responseLocalTime) 不要了   不改动以前逻辑 改动最小
//        GoodListBindItemStatus status = isGroupBookingRow();
        String jgProductType = "";
        if (actPt != null && actPt.assembleStatus == 1 && this.status == 2 && isShowPriceType()) {
            //拼团-售罄
            jgProductType = "拼团";
        } else if (actPt != null && actPt.assembleStatus == 1 && isShowPriceType()) {
            //拼团
            jgProductType = "拼团";
        } else if (actPt != null && actPt.assembleStatus == 0 && isShowPriceType()) {
            //拼团-预热
            jgProductType = "拼团";
        } else if (actSk != null && isShowPriceType()) {
            //秒杀
            jgProductType = "秒杀";
        } else if (actPgby != null && isShowPriceType()) {
            //批购包邮
            jgProductType = "批购包邮";
        } else {
            //平销品
            jgProductType = "普通品";
        }
        return jgProductType;
    }

    public double getJgProductPrice() {
        double jgProductPrice = 0.0;
        // 拼团中的商品，取拼团价/ 秒杀的商品取秒杀价
        if (actPt != null && actPt.assembleStatus == 1) {
            jgProductPrice = actPt.assemblePrice;
        } else if (actSk != null && actSk.status == 1) {
            jgProductPrice = actSk.skPrice;
        } else if (actPt != null && actPt.assembleStatus == 0 && showPriceType() == 0) {
            jgProductPrice = actPt.assemblePrice;
        } else if(actPgby != null && actPgby.getAssemblePrice() != null) {
            jgProductPrice = actPgby.getAssemblePrice();
        } else {
            jgProductPrice = fob;
        }
        return jgProductPrice;
    }

    public double getShowPrice() {
        double showPrice = 0.0;
        // 拼团中的商品，取拼团价/ 秒杀的商品取秒杀价
        if (actPt != null && actPt.assembleStatus == 1) {
            showPrice = actPt.assemblePrice;
        } else if (actSk != null && actSk.status == 1) {
            showPrice = actSk.skPrice;
        } else if (actPt != null && actPt.assembleStatus == 0 && showPriceType() == 0) {
            showPrice = actPt.assemblePrice;
        } else if(actPgby != null && actPgby.getAssemblePrice() != null) {
            showPrice = actPgby.getAssemblePrice();
        } else {
            showPrice = fob;
        }
        return showPrice;
    }

    public SpannableStringBuilder getShowPriceStr() {
        return getShowPriceStr(true);
    }

    public SpannableStringBuilder getShowPriceStrNoPrefix() {
        SpannableStringBuilder ssb = getShowPriceStr(true);
        if (ssb != null && !TextUtils.isEmpty(pricePrefix) && ssb.toString().startsWith(pricePrefix)) {
            ssb.replace(0, pricePrefix.length(), "");
        }
        return ssb;
    }


    /**
     * 生成商品列表价格区展示文案
     *
     * @return
     */
    public SpannableStringBuilder getShowPriceStr(boolean showUnderlinePrice) {
        SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
        switch (showPriceType()) {
            case 0:
                String prefixStr;
                prefixStr = generatePrefixStr(showPriceStr);
                generateGroupPrice(showPriceStr, prefixStr);
                generateUnderlinePrice(showPriceStr, showUnderlinePrice);
                break;
            case -1:
                showPriceStr.append(controlTitle);
                showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_ff982c)), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                break;
        }
        return showPriceStr;
    }

    /**
     * 生成商品列表价格区展示文案
     *
     * @return
     */
    public SpannableStringBuilder getShowPriceStrNew(boolean showUnderlinePrice) {
        SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
        if (!TextUtils.isEmpty(controlTitle)) {
            showPriceStr.append(controlTitle);
            showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_ff982c)), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        } else {
            String prefixStr;
            prefixStr = generatePrefixStr(showPriceStr);
            generateGroupPrice(showPriceStr, prefixStr);
            if (actPt == null && actPgby == null) {
                generateUnderlinePrice(showPriceStr, showUnderlinePrice);
            }
        }
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 文字“签署协议后可见”替换为商品价格；
        if (controlType == 5) {
            showPriceStr = new SpannableStringBuilder();
            String prefixStr;
            prefixStr = generatePrefixStr(showPriceStr);
            generateGroupPrice(showPriceStr, prefixStr);
            if (actPt == null && actPgby == null) {
                generateUnderlinePrice(showPriceStr, showUnderlinePrice);
            }
        }
        return showPriceStr;
    }

    /**
     * 生成商品列表价格区展示文案
     * @param showUnderlinePrice
     * @param isNeedPrefixStr  是否需要前缀  字体大写
     *                          建议不复用  看看符合自己的样式不 这个样式会不太一样
     * @return
     */
    public SpannableStringBuilder getShowPriceStrNew(boolean showUnderlinePrice,boolean isNeedPrefixStr,int prefixSize,int priceSize) {
        SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 首页左右推荐 超值推荐 组件 协议品展示价格
        if (controlType == 5) {
            String prefixStr;
            if (isNeedPrefixStr){
                prefixStr = generatePrefixStr(showPriceStr);
            }else {
                prefixStr = " ¥";
                showPriceStr.append(prefixStr);
                showPriceStr.setSpan(new AbsoluteSizeSpan(prefixSize, true), 0, prefixStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            generateGroupPrice(showPriceStr, prefixStr,priceSize,priceSize);
            if (actPt == null && actPgby == null) {
                generateUnderlinePrice(showPriceStr, showUnderlinePrice);
            }
            return showPriceStr;
        }
        if (!TextUtils.isEmpty(controlTitle)) {
            showPriceStr.append(controlTitle);
            showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_ff982c)), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        } else {
            String prefixStr;
            if (isNeedPrefixStr){
                prefixStr = generatePrefixStr(showPriceStr);
            }else {
                prefixStr = " ¥";
                showPriceStr.append(prefixStr);
                showPriceStr.setSpan(new AbsoluteSizeSpan(prefixSize, true), 0, prefixStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            generateGroupPrice(showPriceStr, prefixStr,priceSize,priceSize);
            if (actPt == null && actPgby == null) {
                generateUnderlinePrice(showPriceStr, showUnderlinePrice);
            }
        }
        return showPriceStr;
    }

//    /**
//     * 生成商品列表价格区展示文案
//     *
//     * @param showUnderlinePrice
//     * @param isNeedNoAgreeAndShowPrice 是否需要判断控制类型为价格签署协议可见状态时显示价格
//     * @param isNeedPrefixStr           是否需要前缀  字体大写
//     *                                  建议不复用  看看符合自己的样式不 这个样式会不太一样
//     * @return
//     */
//    public SpannableStringBuilder getShowPriceStrNew(boolean showUnderlinePrice, boolean isNeedPrefixStr, int prefixSize, int priceSize, boolean isNeedNoAgreeAndShowPrice) {
//        SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
//        if (!TextUtils.isEmpty(controlTitle)) {
//            if (!isNeedNoAgreeAndShowPrice || !isNoAgreeAndShowPrice()){
//                showPriceStr.append(controlTitle);
//                showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_ff982c)), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//                return showPriceStr;
//            }
//        }
//
//        String prefixStr;
//        if (isNeedPrefixStr) {
//            prefixStr = generatePrefixStr(showPriceStr);
//        } else {
//            prefixStr = " ¥";
//            showPriceStr.append(prefixStr);
//            showPriceStr.setSpan(new AbsoluteSizeSpan(prefixSize, true), 0, prefixStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//        }
//        generateGroupPrice(showPriceStr, prefixStr, priceSize, priceSize);
//        if (actPt == null && actPgby == null) {
//            generateUnderlinePrice(showPriceStr, showUnderlinePrice);
//        }
//        return showPriceStr;
//    }

    public SpannableStringBuilder getShowPgbyPriceStrNew(boolean showUnderlinePrice) {
        SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
        if (!TextUtils.isEmpty(controlTitle)) {
            showPriceStr.append(controlTitle);
            showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_ff982c)), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        } else {
            SpannableStringBuilder prefix = new SpannableStringBuilder("¥");
            prefix.setSpan(new AbsoluteSizeSpan(11, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            showPriceStr.append(prefix);
            generatePgbyPrice(showPriceStr,prefix);
            generatePgbyUnderlinePrice(showPriceStr, showUnderlinePrice);
        }
        return showPriceStr;
    }

    public SpannableStringBuilder getShowPgbyPriceStrNewNoUnderline(boolean showUnderlinePrice) {
        SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
        if (!TextUtils.isEmpty(controlTitle)) {
            showPriceStr.append(controlTitle);
            showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_ff982c)), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        } else {
            SpannableStringBuilder prefix = new SpannableStringBuilder("¥");
            prefix.setSpan(new AbsoluteSizeSpan(11, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            showPriceStr.append(prefix);
            generatePgbyPrice(showPriceStr, prefix);
        }
        return showPriceStr;
    }

    private void generateUnderlinePrice(SpannableStringBuilder showPriceStr, boolean showUnderlinePrice) {
        // 拼团的商品，后面增加划线价
        // 秒杀的商品，后面增加划线价（除去全部药品、店铺首页楼层、店铺首页商品）
        if ((actPt != null && actPt.assembleStatus == 1 && actPt.getStepPriceStatus() == 2) || ((actSk != null) && showUnderlinePrice)) {
            showPriceStr.append(" ¥");
            showPriceStr.append(UiUtils.transform(retailPrice));

            showPriceStr.setSpan(new StyleSpan(Typeface.NORMAL), showPriceStr.length() - UiUtils.transform(retailPrice).length() - 1, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            showPriceStr.setSpan(new StrikethroughSpan(), showPriceStr.length() - UiUtils.transform(retailPrice).length() - 1, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            showPriceStr.setSpan(new AbsoluteSizeSpan(11, true), showPriceStr.length() - UiUtils.transform(retailPrice).length() - 1, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_676773)), showPriceStr.length() - UiUtils.transform(retailPrice).length() - 1, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
    }

    private void generatePgbyUnderlinePrice(SpannableStringBuilder showPriceStr, boolean showUnderlinePrice) {
        // 拼团的商品，后面增加划线价
        // 秒杀的商品，后面增加划线价（除去全部药品、店铺首页楼层、店铺首页商品）
        try {
            if (actPgby != null && showUnderlinePrice && actPgby.getAssemblePrice() != null && actPgby.getAssemblePrice() < fob) {
                showPriceStr.append(" ¥");
                showPriceStr.append(UiUtils.transform(fob));
                showPriceStr.setSpan(new StyleSpan(Typeface.NORMAL), showPriceStr.length() - UiUtils.transform(fob).length() - 1, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                showPriceStr.setSpan(new StrikethroughSpan(), showPriceStr.length() - UiUtils.transform(fob).length() - 1, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                showPriceStr.setSpan(new AbsoluteSizeSpan(11, true), showPriceStr.length() - UiUtils.transform(fob).length() - 1, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_676773)), showPriceStr.length() - UiUtils.transform(fob).length() - 1, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 生成
     *
     * @param showPriceStr
     * @return
     */
    @NotNull
    private String generatePrefixStr(SpannableStringBuilder showPriceStr) {
        String prefixStr;
        if (Objects.equals(pricePrefix, "拼团价")){ //不要拼团价
            pricePrefix = "";
        }

        if (!TextUtils.isEmpty(pricePrefix)) {
            prefixStr = pricePrefix + " ¥";
            showPriceStr.append(pricePrefix);
            showPriceStr.setSpan(new StyleSpan(Typeface.NORMAL), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            showPriceStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.color_ff2121)), 0, pricePrefix.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        } else {
            prefixStr = " ¥";
        }
        showPriceStr.append(" ¥");
        showPriceStr.setSpan(new AbsoluteSizeSpan(11, true), 0, prefixStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return prefixStr;
    }


    private void generateGroupPrice(SpannableStringBuilder showPriceStr, String prefixStr) {
        if (showPriceStr == null) return;
        // 拼团中的商品，取拼团价/ 秒杀的商品取秒杀价
        if (actPt != null && actPt.assembleStatus == 1) {
            if (actPt.getStepPriceStatus() == 1) {
                // 多阶梯
                if (!TextUtils.isEmpty(actPt.getMinSkuPrice()) && prefixStr != null) {
                    showPriceStr.append(UiUtils.transform(actPt.getMinSkuPrice()));
                    showPriceStr.setSpan(new ForegroundColorSpan(Color.parseColor("#FF2121")), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    showPriceStr.setSpan(new AbsoluteSizeSpan(16, true), prefixStr.length(), showPriceStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    showPriceStr.setSpan(new AbsoluteSizeSpan(11, true), showPriceStr.length() - 2, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    SpannableStringBuilder showPriceEndStr = new SpannableStringBuilder("起");
                    showPriceEndStr.setSpan(
                            new AbsoluteSizeSpan(11, true),
                            0,
                            1,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    );
                    showPriceStr.append(showPriceEndStr);
                }
//                if (!TextUtils.isEmpty(actPt.getMaxSkuPrice())) {
//                    // SpannableStringBuilder showPriceMaxStr = new SpannableStringBuilder("-" + actPt.getMaxSkuPrice());
//                    SpannableStringBuilder showPriceMaxStr = new SpannableStringBuilder("-");
//                    showPriceMaxStr.append(UiUtils.transform(actPt.getMaxSkuPrice()));
//                    showPriceMaxStr.setSpan(new AbsoluteSizeSpan(16, true), 1, showPriceMaxStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//                    showPriceMaxStr.setSpan(new AbsoluteSizeSpan(11, true), showPriceMaxStr.length() - 2, showPriceMaxStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//                    showPriceStr.append(showPriceMaxStr);
//                }
                return;
            } else {
                if (limitFullDiscountActInfo != null && limitFullDiscountActInfo.getLimitFullDiscount() != null){
                    //限时加补拼团品价格
                    showPriceStr.append(UiUtils.transform(limitFullDiscountActInfo.getLimitFullDiscount()));
                } else{
                    showPriceStr.append(UiUtils.transform(actPt.assemblePrice));
                }
            }
        } else if (actSk != null && actSk.status == 1) {
            showPriceStr.append(UiUtils.transform(actSk.skPrice));
        } else if (actPt != null && actPt.assembleStatus == 0 && showPriceType() == 0) {
            //拼团预热
            if (actPt.preheatShowPrice == 0) {
                //不展示价格
                showPriceStr.append("?");
                showPriceStr.setSpan(new AbsoluteSizeSpan(16, true), prefixStr.length(), showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                return;
            } else {
                //展示价格
                showPriceStr.append(UiUtils.transform(actPt.assemblePrice));
            }
        } else if(actPgby != null && actPgby.getAssemblePrice() != null) {
            showPriceStr.append(UiUtils.transform(actPgby.getAssemblePrice()));
        } else {
            showPriceStr.append(UiUtils.transform(fob));
        }
        showPriceStr.setSpan(new AbsoluteSizeSpan(16, true), prefixStr.length(), showPriceStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        showPriceStr.setSpan(new AbsoluteSizeSpan(11, true), showPriceStr.length() - 2, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    }


    private void generateGroupPrice(SpannableStringBuilder showPriceStr, String prefixStr,int pricePreSize,int priceEndSize) {
        if (showPriceStr == null) return;
        // 拼团中的商品，取拼团价/ 秒杀的商品取秒杀价
        if (actPt != null && actPt.assembleStatus == 1) {
            if (actPt.getStepPriceStatus() == 1) {
                // 多阶梯
                if (!TextUtils.isEmpty(actPt.getMinSkuPrice()) && prefixStr != null) {
                    showPriceStr.append(UiUtils.transform(actPt.getMinSkuPrice()));
                    showPriceStr.setSpan(new ForegroundColorSpan(Color.parseColor("#FF2121")), 0, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    showPriceStr.setSpan(new AbsoluteSizeSpan(pricePreSize, true), prefixStr.length(), showPriceStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    showPriceStr.setSpan(new AbsoluteSizeSpan(priceEndSize, true), showPriceStr.length() - 2, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    SpannableStringBuilder showPriceEndStr = new SpannableStringBuilder("起");
                    showPriceEndStr.setSpan(
                            new AbsoluteSizeSpan(pricePreSize, true),
                            0,
                            1,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    );
                    showPriceStr.append(showPriceEndStr);
                }
//                if (!TextUtils.isEmpty(actPt.getMaxSkuPrice())) {
//                    SpannableStringBuilder showPriceMaxStr = new SpannableStringBuilder("-" + actPt.getMaxSkuPrice());
//                    showPriceMaxStr.setSpan(new AbsoluteSizeSpan(pricePreSize, true), 1, showPriceMaxStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//                    showPriceMaxStr.setSpan(new AbsoluteSizeSpan(priceEndSize, true), showPriceMaxStr.length() - 2, showPriceMaxStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//                    showPriceStr.append(showPriceMaxStr);
//                }
                return;
            } else {
                showPriceStr.append(UiUtils.transform(actPt.assemblePrice));
            }
        } else if (actSk != null && actSk.status == 1) {
            showPriceStr.append(UiUtils.transform(actSk.skPrice));
        } else if (actPt != null && actPt.assembleStatus == 0 && showPriceType() == 0) {
            showPriceStr.append(UiUtils.transform(actPt.assemblePrice));
        } else if(actPgby != null && actPgby.getAssemblePrice() != null) {
            showPriceStr.append(UiUtils.transform(actPgby.getAssemblePrice()));
        } else {
            showPriceStr.append(UiUtils.transform(fob));
        }
        showPriceStr.setSpan(new AbsoluteSizeSpan(pricePreSize, true), prefixStr.length(), showPriceStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        showPriceStr.setSpan(new AbsoluteSizeSpan(priceEndSize, true), showPriceStr.length() - 2, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    }

    private void generatePgbyPrice(SpannableStringBuilder showPriceStr, SpannableStringBuilder prefix) {
        if (actPgby == null) return;
        if (actPgby.getAssemblePrice() == null) return;
        showPriceStr.append(UiUtils.transform(actPgby.getAssemblePrice()));
        showPriceStr.setSpan(new AbsoluteSizeSpan(16, true), prefix.length(), showPriceStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        showPriceStr.setSpan(new AbsoluteSizeSpan(11, true), showPriceStr.length() - 2, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    }

    /**
     * 展示优先级：
     * 1. 建议零售价
     * 2. 控销价格
     *
     * @return
     */
    public String getShowSuggestOrGrossMargin() {
        String showSuggestorGrossMarginStr = "";

        //建议零售价
        if (!TextUtils.isEmpty(suggestPrice) && !TextUtils.isEmpty(grossMargin)) {
            showSuggestorGrossMarginStr = "零售价¥" + suggestPrice + " 毛利率" + grossMargin;
        }
        //控销价
        if (!TextUtils.isEmpty(uniformPrice) && !TextUtils.isEmpty(grossMargin)) {
            showSuggestorGrossMarginStr = "控销价¥" + uniformPrice + " 毛利率" + grossMargin;
        }
        return showSuggestorGrossMarginStr;
    }

    /**
     * 展示优先级：
     * 1. 建议零售价
     * 2. 控销价格
     *
     * @return
     */
    public String getShowSuggestOrGrossMargin2() {
        String showSuggestorGrossMarginStr = "";

        //建议零售价
        if (!TextUtils.isEmpty(suggestPrice) && !TextUtils.isEmpty(grossMargin)) {
            showSuggestorGrossMarginStr = "零售价¥" + suggestPrice + " | " + " 毛利率" + grossMargin;
        }
        //控销价
        if (!TextUtils.isEmpty(uniformPrice) && !TextUtils.isEmpty(grossMargin)) {
            showSuggestorGrossMarginStr = "控销价¥" + uniformPrice + " | " + " 毛利率" + grossMargin;
        }
        return showSuggestorGrossMarginStr;
    }

    //endregion
    //====================================

    public int getHighGross() {
        return highGross;
    }

    public void setHighGross(int highGross) {
        this.highGross = highGross;
    }

    public ActivityTagBean getActivityTag() {
        return activityTag;
    }

    public void setActivityTag(ActivityTagBean activityTag) {
        this.activityTag = activityTag;
    }

    public int getIsUsableMedicalStr() {
        return IsUsableMedicalStr;
    }

    public void setIsUsableMedicalStr(int isUsableMedicalStr) {
        IsUsableMedicalStr = isUsableMedicalStr;
    }

    public String getThirtyDaysAmount() {
        return thirtyDaysAmount;
    }

    public void setThirtyDaysAmount(String thirtyDaysAmount) {
        this.thirtyDaysAmount = thirtyDaysAmount;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getCompanyName() {
        return companyName;
    }


    public void setBusinessType(int businessType) {
        this.businessType = businessType;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public int getIsThirdCompany() {
        return isThirdCompany;
    }

    public void setIsThirdCompany(int isThirdCompany) {
        this.isThirdCompany = isThirdCompany;
    }

    public double getReducePrice() {
        return reducePrice;
    }

    public void setReducePrice(double reducePrice) {
        this.reducePrice = reducePrice;
    }

    public String getNearEffect() {
        return nearEffect;
    }

    public void setNearEffect(String nearEffect) {
        this.nearEffect = nearEffect;
    }

    public String getFarEffect() {
        return farEffect;
    }

    public void setFarEffect(String farEffect) {
        this.farEffect = farEffect;
    }

    public int getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(int signStatus) {
        this.signStatus = signStatus;
    }

    public boolean getIsOEM() {
        return isOEM;
    }

    public void setIsOEM(boolean isOEM) {
        this.isOEM = isOEM;
    }

    public int getAgreementEffective() {
        return agreementEffective;
    }

    public void setAgreementEffective(int agreementEffective) {
        this.agreementEffective = agreementEffective;
    }

    public boolean isReducePrice() {
        return reducePrice > 0;
    }

    public boolean isMarkerUrl() {
        return !TextUtils.isEmpty(markerUrl);
    }

    public boolean isFavoriteStatus() {
        return favoriteStatus == 1;
    }

    public boolean isArrivalReminder() {
        return arrivalReminder;
    }

    public void setArrivalReminder(boolean arrivalReminder) {
        this.arrivalReminder = arrivalReminder;
    }

    public void setFavoriteStatus(int favoriteStatus) {
        this.favoriteStatus = favoriteStatus;
    }

    public int getBusinessType() {
        return businessType;
    }

    public String getFavoriterDiffPriceTag() {
        return favoriterDiffPriceTag;
    }

    public int getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(int productNumber) {
        this.productNumber = productNumber;
    }

    public List<LabelIconBean> getTagList() {
        return tagList;
    }

    public boolean getIsExpire() {
        return isExpire != 1;
    }

    public void setTagList(List<LabelIconBean> tagList) {
        this.tagList = tagList;
    }

    public int getAgent() {
        return agent;
    }

    public int getPriceType() {
        return priceType;
    }

    public String getBlackBGTextDes() {
        return blackBGTextDes;
    }

    public long getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setSkuId(long id) {
        this.id = id;
    }

    public int getAvailableQty() {
        return availableQty;
    }

    public void setAvailableQty(int availableQty) {
        this.availableQty = availableQty;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getRetailPrice() {
        return retailPrice;
    }

    public double getFob() {
        return fob;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public int getIsControl() {
        return isControl;
    }


    public boolean isPurchase() {
        return isPurchase;
    }

    @Override
    public String getProductName() {
        return showName;
    }

    @Override
    public String getProductSpec() {
        return spec;
    }

    @Override
    public String getProductPrice() {
        return getFob() + "";
    }

    @Override
    public String getOldPrice() {
        return getRetailPrice() + "";
    }

    @Override
    public String getProductImg() {
        return imageUrl;
    }

    @Override
    public String getMarkerUrl() {
        if (TextUtils.isEmpty(markerUrl)) {
            return "";
        } else {
            return markerUrl;
        }
    }

    @Override
    public String getProductId() {
        return getId() + "";
    }

    @Override
    public boolean isSoldOut() {
        return getStatus() == 2 || getStatus() == 4;
    }

    @Override
    public boolean isGift() {
        return gift;
    }

    @Override
    public boolean isStart() {
        return System.currentTimeMillis() >= seckillStartTime;
    }

    public String getGrossMargin() {
        return grossMargin;
    }

    public String getUniformPrice() {
        return uniformPrice;
    }

    public String getSuggestPrice() {
        return suggestPrice;
    }

    public List<PriceRangeListBean> getSkuPriceRangeList() {
        return skuPriceRangeList;
    }

    public int getStepNum() {
        return mediumPackageNum;
    }

    public int getIsSplit() {
        return isSplit;
    }

    public String getMediumPackageTitle() {
        return mediumPackageTitle;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RowsBean rowsBean = (RowsBean) o;

        if (id != rowsBean.id) return false;
        return showName != null ? showName.equals(rowsBean.showName) : rowsBean.showName == null;
    }

    @Override
    public int hashCode() {
        int result = (int) id;
        result = 31 * result + (showName != null ? showName.hashCode() : 0);
        return result;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {

        dest.writeInt(spanSize);
        dest.writeLong(id);
        dest.writeString(showName);
        dest.writeString(manufacturer);
        dest.writeString(spec);
        dest.writeLong(seckillStartTime);
        dest.writeInt(status);
        dest.writeString(retailPrice);
        dest.writeDouble(fob);
        dest.writeInt(availableQty);
        dest.writeString(imageUrl);
        dest.writeInt(favoriteStatus);
        dest.writeInt(isControl);
        dest.writeByte((byte) (isPurchase ? 1 : 0));
        dest.writeString(markerUrl);
        dest.writeInt(mediumPackageNum);
        dest.writeInt(isSplit);
        dest.writeString(isSplitTitle);
        dest.writeString(mediumPackageTitle);
        dest.writeString(grossMargin);
        dest.writeString(uniformPrice);
        dest.writeString(suggestPrice);
        dest.writeString(blackBGTextDes);
        dest.writeInt(priceType);
        dest.writeInt(agent);
        dest.writeInt(productNumber);
        dest.writeInt(isExpire);
        dest.writeString(favoriterDiffPriceTag);
        dest.writeInt(businessType);
        dest.writeInt(signStatus);
        dest.writeByte((byte) (isOEM ? 1 : 0));
        dest.writeInt(agreementEffective);
        dest.writeString(nearEffect);
        dest.writeString(farEffect);
        dest.writeDouble(reducePrice);
        dest.writeByte((byte) (gift ? 1 : 0));
        dest.writeString(companyName);
        dest.writeInt(isThirdCompany);
        dest.writeString(orgId);
        dest.writeString(thirtyDaysAmount);
        dest.writeInt(IsUsableMedicalStr);
        dest.writeByte((byte) (arrivalReminder ? 1 : 0));
        dest.writeInt(highGross);
        dest.writeString(appUrl);
    }

    protected RowsBean(Parcel in) {
        spanSize = in.readInt();
        id = in.readLong();
        showName = in.readString();
        manufacturer = in.readString();
        spec = in.readString();
        seckillStartTime = in.readLong();
        status = in.readInt();
        retailPrice = in.readString();
        fob = in.readDouble();
        availableQty = in.readInt();
        imageUrl = in.readString();
        favoriteStatus = in.readInt();
        isControl = in.readInt();
        isPurchase = in.readByte() != 0;
        markerUrl = in.readString();
        mediumPackageNum = in.readInt();
        isSplit = in.readInt();
        isSplitTitle = in.readString();
        mediumPackageTitle = in.readString();
        grossMargin = in.readString();
        uniformPrice = in.readString();
        suggestPrice = in.readString();
        blackBGTextDes = in.readString();
        priceType = in.readInt();
        agent = in.readInt();
        productNumber = in.readInt();
        isExpire = in.readInt();
        favoriterDiffPriceTag = in.readString();
        businessType = in.readInt();
        signStatus = in.readInt();
        isOEM = in.readByte() != 0;
        agreementEffective = in.readInt();
        nearEffect = in.readString();
        farEffect = in.readString();
        reducePrice = in.readDouble();
        gift = in.readByte() != 0;
        companyName = in.readString();
        isThirdCompany = in.readInt();
        orgId = in.readString();
        thirtyDaysAmount = in.readString();
        IsUsableMedicalStr = in.readInt();
        arrivalReminder = in.readByte() != 0;
        highGross = in.readInt();
        appUrl = in.readString();
    }

    public static final Creator<RowsBean> CREATOR = new Creator<RowsBean>() {
        @Override
        public RowsBean createFromParcel(Parcel in) {
            return new RowsBean(in);
        }

        @Override
        public RowsBean[] newArray(int size) {
            return new RowsBean[size];
        }
    };

    @Override
    public int getItemType() {
        return 0;
    }

    public boolean isCanAddToCart() {
        return canAddToCart;
    }

    public void setCanAddToCart(boolean canAddToCart) {
        this.canAddToCart = canAddToCart;
    }

    public boolean isFreeShippingFlag() {
        return freeShippingFlag;
    }

    public void setFreeShippingFlag(boolean freeShippingFlag) {
        this.freeShippingFlag = freeShippingFlag;
    }

    public String getFreeShippingText() {
        return freeShippingText;
    }

    public void setFreeShippingText(String freeShippingText) {
        this.freeShippingText = freeShippingText;
    }

    /**
     * 是否显示组合购
     * @return
     */
    public boolean showGroupPurchase(){
        return enterProductDetailIsShowRecPurchase == 1 && enterProductDetailShowRecPurchaseType == 1;
    }

    /**
     * 是否显示加价购
     * @return
     */
    public boolean showAdditinalPurchase(){
        return enterProductDetailIsShowRecPurchase == 1 && enterProductDetailShowRecPurchaseType == 2;
    }
}
